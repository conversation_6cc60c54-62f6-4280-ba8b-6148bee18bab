@import "tailwindcss";

/* PlayersClub Design Variables */
:root {
  font-size: 16px;
  --color-text: #000;
  --color-text-alt: #6a6a6a;
  --color-faded: #c9c9c9;
  --color-bg: #fff;
  --border-color: #ccc;
  --color-link: #000;
  --color-link-hover: #999;
  --color-placeholder: #f8f8f8;
  --font-size-s: 0.85rem;
  --font-size-l: clamp(1.25rem, 3vw, 1.75rem);
  --font-size-xl: clamp(1.5rem, 5vw, 2.75rem);
}

* {
  box-sizing: border-box;

  &::after,
  &::before {
    box-sizing: inherit;
  }
}

body {
  margin: 0;
  padding: 0 1rem;
  color: var(--color-text);
  background-color: var(--color-bg);
  line-height: 1;
  font-family: "Geist Variable", sans-serif;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* PlayersClub Header Styling */
.title-header {
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  font-weight: 700;
  gap: 1rem;
  align-items: end;
  padding: 2rem 0 1.5rem;
  width: 100%;
}

.title-header--initial {
  font-size: var(--font-size-l);

  @media screen and (min-width: 50em) {
    grid-template-columns: 20vw 1fr auto;
    gap: 1rem;
  }
}

/* PlayersClub Grid Styling */
.artist-grid {
  position: relative;
  overflow: hidden;
  display: grid;
  width: 100%;
  grid-template-columns: repeat(2, calc(100% / 2));
  margin: 1rem 0 10rem;
  min-height: 400px;
}

@media screen and (min-width: 30em) {
  .artist-grid {
    grid-template-columns: repeat(4, calc(100% / 4));
  }
}

@media screen and (min-width: 50em) {
  .artist-grid {
    grid-template-columns: repeat(7, calc(100% / 7));
  }
}

/* Artist Card Styling - exact PlayersClub */
.artist {
  background-size: cover;
  background-position: 50% 0%;
  aspect-ratio: 0.8;
  display: block;
  text-decoration: none;
}

/* Separator */
hr {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: 0;
}

/* Button Styling */
button {
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  cursor: pointer;
  padding: 0;
}

button:hover {
  color: var(--color-link-hover);
}

/* Search Input */
input[type="text"] {
  background: transparent;
  border: none;
  border-bottom: 2px solid var(--border-color);
  color: var(--color-text);
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1rem 0;
  width: 100%;
  outline: none;
}

input[type="text"]:focus {
  border-bottom-color: var(--color-text);
}

input[type="text"]::placeholder {
  color: var(--color-text-alt);
}

/* Header Button Layout */
.title-header div {
  display: flex;
  list-style: none;
  align-items: center;
  gap: 1rem;
  margin: 0;
  padding: 0;
}

.title-header div:has(> .search) {
  position: relative;
}

.title-header div:has(> .search--active) {
  margin-left: 1rem;
}

.title-header div:has(> .search--active)::before {
  pointer-events: none;
  content: "";
  position: absolute;
  left: -1rem;
  right: -1rem;
  top: -0.5rem;
  bottom: -0.5rem;
  border: 1px solid var(--color-text);
  border-radius: 2rem;
}

.search--active:focus-visible {
  color: inherit;
}

/* Clear button styling */
.clear {
  margin-left: 0.5rem;
  opacity: 0.7;
}

.clear:hover {
  opacity: 1;
}

/* Tooltip Styling */
.tooltip {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  padding: 1rem;
  width: 240px;
  background: #000;
  color: #bababa;
  display: grid;
  grid-template-columns: 1fr auto;
  grid-template-rows: auto auto;
  pointer-events: none;
  justify-content: space-between;
  grid-row-gap: 0.25rem;
  will-change: transform;
  z-index: 1000;
}

.tooltip__row {
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  grid-template-areas: "tt-content";
  font-size: 12px;
}

.tooltip__row--ra {
  justify-self: end;
  text-align: right;
}

.tooltip__row:first-child {
  text-transform: uppercase;
  font-weight: bold;
  color: #fff;
}

.tooltip__content {
  grid-area: tt-content;
  white-space: nowrap;
}

/* Dialog Search Modal Styling - exact PlayersClub */
dialog {
  filter: none;
  pointer-events: auto;
  border: 0;
  padding: 3rem;
  width: clamp(300px, 40vw, 500px);
  background: var(--color-text);
  color: var(--color-bg);
}

dialog::backdrop {
  background: rgba(0, 0, 0, 0.8);
}

dialog button {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

dialog input {
  background: none;
  width: 100%;
  font-size: var(--font-size-l);
  border: 0;
  color: inherit;
  font-weight: bold;
  padding: 0.25rem 0;
  border-bottom: 1px solid var(--color-faded);
}

dialog input:focus {
  outline: none;
}

dialog input::placeholder {
  color: var(--color-faded);
  opacity: 0.4;
}

dialog span {
  font-size: var(--font-size-s);
  color: var(--color-faded);
  margin-top: 0.5rem;
  display: block;
}

/* Page blur effect when dialog is open */
body.blurred > *:not(dialog) {
  filter: blur(3px);
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* TextSlider Component Styling */
.oh {
  overflow: hidden;
  position: relative;
  display: inline-block;
}

.oh__inner {
  display: inline-block;
  will-change: transform;
}

/* Preloader Styling */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.3s ease;
}

.loading.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading__content {
  text-align: center;
  color: var(--color-text);
}

.loading__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-faded);
  border-top: 3px solid var(--color-text);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading__content p {
  margin: 0;
  font-size: var(--font-size-s);
  color: var(--color-text-alt);
}
