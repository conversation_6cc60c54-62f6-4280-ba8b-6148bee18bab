"use client";

import { useState } from "react";
import { champions } from "@/data/champions";
import SearchModal from "@/components/SearchModal";

export default function Home() {
  const [displayedChampions, setDisplayedChampions] = useState(champions);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const shuffleChampions = () => {
    const shuffled = [...champions].sort(() => Math.random() - 0.5);
    setDisplayedChampions(shuffled);
  };

  const sortChampions = () => {
    const sorted = [...champions].sort((a, b) => a.name.localeCompare(b.name));
    setDisplayedChampions(sorted);
  };

  return (
    <div className="min-h-screen">
      {/* Header Section - exact PlayersClub design */}
      <header className="title-header title-header--initial">
        <h2>Champions</h2>
        <div>
          <button onClick={sortChampions} data-sort>
            Sort
          </button>
          <button onClick={shuffleChampions} data-shuffle>
            Shuffle
          </button>
          <div>
            <button
              className="search"
              data-search
              onClick={() => setIsSearchOpen(true)}
            >
              Search
            </button>
          </div>
        </div>
        <span>{displayedChampions.length}</span>
      </header>

      {/* Separator */}
      <hr />

      {/* Champions Grid - exact PlayersClub layout with just photos */}
      <section className="artist-grid" id="grid" data-grid="champions">
        {displayedChampions.map((champion) => (
          <a
            key={champion.id}
            href={`/champion/${champion.slug}`}
            className="artist"
            style={{
              backgroundImage: `url('${champion.image}')`,
              backgroundSize: "cover",
              backgroundPosition: "50% 0%",
              aspectRatio: "0.8",
            }}
            data-name={champion.name}
            data-stagename={champion.stageName}
            data-sport={champion.sport}
            aria-label={champion.name}
          />
        ))}
      </section>

      {/* Tooltip */}
      <div className="tooltip" data-grid="champions">
        <div className="tooltip__row" data-field="stagename">
          <div className="tooltip__content"></div>
          <div className="tooltip__content"></div>
        </div>
        <div className="tooltip__row tooltip__row--ra">→</div>
        <div className="tooltip__row" data-field="name">
          <div className="tooltip__content"></div>
          <div className="tooltip__content"></div>
        </div>
        <div className="tooltip__row tooltip__row--ra" data-field="sport">
          <div className="tooltip__content"></div>
          <div className="tooltip__content"></div>
        </div>
      </div>

      {/* Search Modal */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </div>
  );
}
