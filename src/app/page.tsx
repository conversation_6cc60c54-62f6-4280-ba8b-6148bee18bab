"use client";

import { useState, useEffect } from "react";
import { champions } from "@/data/champions";
import SearchModal from "@/components/SearchModal";
import Tooltip from "@/components/Tooltip";
import TextSlider from "@/components/TextSlider";
import Preloader from "@/components/Preloader";

export default function Home() {
  const [displayedChampions, setDisplayedChampions] = useState(champions);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  useEffect(() => {
    // Set data-page attribute for animations
    document.documentElement.setAttribute("data-page", "home");

    // Initialize animations directly with dynamic imports
    const initAnimations = async () => {
      const { gsap } = await import("gsap");

      // DOM elements and animation-related variables
      const lines = document.querySelectorAll("hr");
      const textSliders = document.querySelectorAll("header .oh > .oh__inner");
      const gridContainer = document.querySelector("[data-grid]");
      const gridItems = gridContainer ? Array.from(gridContainer.children) : [];
      const hasPreloaderComponent = document.querySelector(".loading");

      // Animate the homepage elements using a GSAP timeline.
      const animateHomepageElements = () => {
        if (!gridContainer || !gridItems.length) return;

        // Hide the grid container before starting the animation.
        gsap.set(gridContainer, { autoAlpha: 0 });

        gsap
          .timeline({
            defaults: {
              duration: 1.4,
              ease: "power4",
            },
            onComplete: () => {
              // Dispatch a custom event after all animations complete.
              const event = new CustomEvent("gridRendered");
              document.dispatchEvent(event);
            },
          })
          .fromTo(
            lines,
            { transformOrigin: "0% 50%", scaleX: 0 },
            { duration: 1.6, ease: "power2", stagger: 0.9, scaleX: 1 }
          )
          .from(textSliders, { yPercent: 100, stagger: 0.1 }, 0.2)
          .set(gridContainer, { autoAlpha: 1 }, "<+=1")
          .from(gridItems, { yPercent: 100, stagger: 0.08 }, "<")
          .from(gridItems, { ease: "sine", autoAlpha: 0, stagger: 0.08 }, "<");
      };

      // Disable scroll restoration on browser back navigation.
      if ("scrollRestoration" in history) {
        history.scrollRestoration = "manual";
      }
      // Scroll to the top of the page.
      window.scrollTo(0, 0);

      // Wait for assets to load if a preloader is present.
      if (
        hasPreloaderComponent &&
        sessionStorage.getItem("preloadComplete") !== "true"
      ) {
        document.addEventListener("assetsLoaded", animateHomepageElements, {
          once: true,
        });
      } else {
        animateHomepageElements();
      }
    };

    initAnimations();
  }, []);

  const shuffleChampions = () => {
    const shuffled = [...champions].sort(() => Math.random() - 0.5);
    setDisplayedChampions(shuffled);
  };

  const sortChampions = () => {
    const sorted = [...champions].sort((a, b) => a.name.localeCompare(b.name));
    setDisplayedChampions(sorted);
  };

  return (
    <div className="min-h-screen">
      {/* Preloader for smooth animations */}
      <Preloader />

      {/* Header Section - exact PlayersClub design with TextSlider */}
      <header className="title-header title-header--initial">
        <h2>
          <TextSlider content="Champions" />
        </h2>
        <div>
          <button onClick={sortChampions} data-sort>
            <TextSlider content="Sort" />
          </button>
          <button onClick={shuffleChampions} data-shuffle>
            <TextSlider content="Shuffle" />
          </button>
          <div>
            <button
              className="search"
              data-search
              onClick={() => setIsSearchOpen(true)}
            >
              <TextSlider content="Search" />
            </button>
          </div>
        </div>
        <span>
          <TextSlider content={displayedChampions.length} />
        </span>
      </header>

      {/* Separator */}
      <hr />

      {/* Champions Grid - exact PlayersClub layout with just photos */}
      <section className="artist-grid" id="grid" data-grid="champions">
        {displayedChampions.map((champion) => (
          <a
            key={champion.id}
            href={`/champion/${champion.slug}`}
            className="artist"
            style={{
              backgroundImage: `url('${champion.image}')`,
              backgroundSize: "cover",
              backgroundPosition: "50% 0%",
              aspectRatio: "0.8",
            }}
            data-name={champion.name}
            data-stagename={champion.stageName}
            data-sport={champion.sport}
            aria-label={champion.name}
          />
        ))}
      </section>

      {/* Tooltip with GSAP animations */}
      <Tooltip grid="champions" />

      {/* Search Modal */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </div>
  );
}
