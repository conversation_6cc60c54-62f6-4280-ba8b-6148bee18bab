"use client";

import { useState, useEffect } from "react";
import { champions } from "@/data/champions";
import SearchModal from "@/components/SearchModal";
import Tooltip from "@/components/Tooltip";
import TextSlider from "@/components/TextSlider";
import Preloader from "@/components/Preloader";

export default function Home() {
  const [displayedChampions, setDisplayedChampions] = useState(champions);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  useEffect(() => {
    // Set data-page attribute for animations
    document.documentElement.setAttribute("data-page", "home");

    // Load animation scripts
    const indexScript = document.createElement("script");
    indexScript.src = "/scripts/index.js";
    indexScript.type = "module";
    document.head.appendChild(indexScript);

    const gridActionsScript = document.createElement("script");
    gridActionsScript.src = "/scripts/gridActions.js";
    gridActionsScript.type = "module";
    document.head.appendChild(gridActionsScript);

    return () => {
      document.head.removeChild(indexScript);
      document.head.removeChild(gridActionsScript);
    };
  }, []);

  const shuffleChampions = () => {
    const shuffled = [...champions].sort(() => Math.random() - 0.5);
    setDisplayedChampions(shuffled);
  };

  const sortChampions = () => {
    const sorted = [...champions].sort((a, b) => a.name.localeCompare(b.name));
    setDisplayedChampions(sorted);
  };

  return (
    <div className="min-h-screen">
      {/* Preloader for smooth animations */}
      <Preloader />

      {/* Header Section - exact PlayersClub design with TextSlider */}
      <header className="title-header title-header--initial">
        <h2>
          <TextSlider content="Champions" />
        </h2>
        <div>
          <button onClick={sortChampions} data-sort>
            <TextSlider content="Sort" />
          </button>
          <button onClick={shuffleChampions} data-shuffle>
            <TextSlider content="Shuffle" />
          </button>
          <div>
            <button
              className="search"
              data-search
              onClick={() => setIsSearchOpen(true)}
            >
              <TextSlider content="Search" />
            </button>
          </div>
        </div>
        <span>
          <TextSlider content={displayedChampions.length} />
        </span>
      </header>

      {/* Separator */}
      <hr />

      {/* Champions Grid - exact PlayersClub layout with just photos */}
      <section className="artist-grid" id="grid" data-grid="champions">
        {displayedChampions.map((champion) => (
          <a
            key={champion.id}
            href={`/champion/${champion.slug}`}
            className="artist"
            style={{
              backgroundImage: `url('${champion.image}')`,
              backgroundSize: "cover",
              backgroundPosition: "50% 0%",
              aspectRatio: "0.8",
            }}
            data-name={champion.name}
            data-stagename={champion.stageName}
            data-sport={champion.sport}
            aria-label={champion.name}
          />
        ))}
      </section>

      {/* Tooltip with GSAP animations */}
      <Tooltip grid="champions" />

      {/* Search Modal */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />
    </div>
  );
}
