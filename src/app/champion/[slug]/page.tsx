import { notFound } from 'next/navigation';
import { getChampionBySlug, champions } from '@/data/champions';
import ChampionDetail from '@/components/ChampionDetail';

interface ChampionPageProps {
  params: {
    slug: string;
  };
}

export async function generateStaticParams() {
  return champions.map((champion) => ({
    slug: champion.slug,
  }));
}

export async function generateMetadata({ params }: ChampionPageProps) {
  const champion = getChampionBySlug(params.slug);

  if (!champion) {
    return {
      title: 'Champion Not Found',
    };
  }

  return {
    title: `${champion.name} - ${champion.stageName} | UA Champions`,
    description: champion.description,
    keywords: `${champion.name}, ${champion.stageName}, ${champion.sport}, Ukrainian champion, boxing`,
  };
}

export default function ChampionPage({ params }: ChampionPageProps) {
  const champion = getChampionBySlug(params.slug);

  if (!champion) {
    notFound();
  }

  return <ChampionDetail champion={champion} />;
}
