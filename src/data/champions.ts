import { Champion } from "@/types/champion";

export const champions: Champion[] = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    stageName: "Dr. <PERSON>",
    slug: "vitali-klitschko",
    sport: "Boxing",
    nationality: "Ukrainian",
    birthDate: "1971-07-19",
    achievements: [
      "WBC Heavyweight Champion (2004-2005, 2008-2013)",
      "WBO Heavyweight Champion (1999-2000)",
      "Mayor of Kyiv (2014-present)",
      "Longest combined heavyweight title reign of the 21st century",
    ],
    description:
      "<PERSON><PERSON> is a Ukrainian former professional boxer and politician. Known for his exceptional defensive skills and powerful left hook, he held multiple heavyweight world championships and is considered one of the greatest heavyweight boxers of all time.",
    image: "/images/vitali-klitschko.svg",
    detailImages: [
      "/images/vitali-klitschko.svg",
      "/images/vitali-klitschko.svg",
      "/images/vitali-klitschko.svg",
      "/images/vitali-klitschko.svg",
    ],
    stats: {
      fights: 47,
      wins: 45,
      losses: 2,
      draws: 0,
      knockouts: 41,
    },
    titles: [
      "WBC Heavyweight Champion",
      "WBO Heavyweight Champion",
      "European Heavyweight Champion",
    ],
    career: {
      start: "1996",
      end: "2012",
      status: "retired",
    },
  },
  {
    id: "2",
    name: "Wladimir Klitschko",
    stageName: "Dr. Steelhammer",
    slug: "wladimir-klitschko",
    sport: "Boxing",
    nationality: "Ukrainian",
    birthDate: "1976-03-25",
    achievements: [
      "Unified Heavyweight Champion (2006-2015)",
      "WBA (Super), IBF, WBO, IBO Heavyweight Champion",
      "Olympic Gold Medalist (1996)",
      "Longest heavyweight title reign in boxing history",
    ],
    description:
      "Wladimir Klitschko is a Ukrainian former professional boxer who competed from 1996 to 2017. He held the world heavyweight championship twice, including the unified WBA (Super), IBF, WBO, IBO, and Ring magazine titles.",
    image: "/images/wladimir-klitschko.svg",
    detailImages: [
      "/images/wladimir-klitschko.svg",
      "/images/wladimir-klitschko.svg",
      "/images/wladimir-klitschko.svg",
      "/images/wladimir-klitschko.svg",
    ],
    stats: {
      fights: 69,
      wins: 64,
      losses: 5,
      draws: 0,
      knockouts: 53,
    },
    titles: [
      "WBA (Super) Heavyweight Champion",
      "IBF Heavyweight Champion",
      "WBO Heavyweight Champion",
      "IBO Heavyweight Champion",
    ],
    career: {
      start: "1996",
      end: "2017",
      status: "retired",
    },
  },
  {
    id: "3",
    name: "Oleksandr Usyk",
    stageName: "The Cat",
    slug: "oleksandr-usyk",
    sport: "Boxing",
    nationality: "Ukrainian",
    birthDate: "1987-01-17",
    achievements: [
      "Undisputed Heavyweight Champion (2024-present)",
      "Undisputed Cruiserweight Champion (2018)",
      "Olympic Gold Medalist (2012)",
      "First boxer to hold all four major world championships in two weight classes",
    ],
    description:
      "Oleksandr Usyk is a Ukrainian professional boxer who has held the undisputed heavyweight championship since 2024. He previously held the undisputed cruiserweight championship from 2018 to 2019, being the first boxer to hold all four major world championships in the cruiserweight division.",
    image: "/images/oleksandr-usyk.svg",
    detailImages: [
      "/images/oleksandr-usyk.svg",
      "/images/oleksandr-usyk.svg",
      "/images/oleksandr-usyk.svg",
      "/images/oleksandr-usyk.svg",
    ],
    stats: {
      fights: 22,
      wins: 22,
      losses: 0,
      draws: 0,
      knockouts: 14,
    },
    titles: [
      "WBA (Super) Heavyweight Champion",
      "WBC Heavyweight Champion",
      "IBF Heavyweight Champion",
      "WBO Heavyweight Champion",
    ],
    career: {
      start: "2013",
      status: "active",
    },
  },
];

export const getChampionBySlug = (slug: string): Champion | undefined => {
  return champions.find((champion) => champion.slug === slug);
};

export const searchChampions = (query: string): Champion[] => {
  if (!query.trim()) return champions;

  const lowercaseQuery = query.toLowerCase();
  return champions.filter(
    (champion) =>
      champion.name.toLowerCase().includes(lowercaseQuery) ||
      champion.stageName.toLowerCase().includes(lowercaseQuery) ||
      champion.sport.toLowerCase().includes(lowercaseQuery)
  );
};
