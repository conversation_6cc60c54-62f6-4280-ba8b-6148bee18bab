---
import Cross from "../assets/images/cross.svg";
---

<dialog id="search-dialog">
  <form method="dialog">
    <label for="search-input" class="sr-only">Search:</label>
    <input
      aria-label="search"
      type="text"
      id="search-input"
      placeholder="Search"
      maxlength="30"
    />
    <span>Enter a name or stage name</span>
    <button id="close-dialog" arial-label="Close search"
      ><Cross width={9} height={9} /></button
    >
  </form>
</dialog>

<style>
  dialog {
    filter: none; /* Ensure the dialog itself is not blurred */
    pointer-events: auto; /* Allow interaction with the dialog */
    border: 0;
    padding: 3rem;
    width: clamp(300px, 40vw, 500px);
    background: var(--color-text);
    color: var(--color-bg);
  }
  button {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }
  input {
    background: none;
    width: 100%;
    font-size: var(--font-size-l);
    border: 0;
    color: inherit;
    font-weight: bold;
    padding: 0.25rem 0;
    border-bottom: 1px solid var(--color-faded);
  }
  input:focus {
    outline: none;
  }

  input::placeholder {
    color: var(--color-faded);
    opacity: 0.4;
  }

  span {
    font-size: var(--font-size-s);
    color: var(--color-faded);
    margin-top: 0.5rem;
    display: block;
  }
</style>
