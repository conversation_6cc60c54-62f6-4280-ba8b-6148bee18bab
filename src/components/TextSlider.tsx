"use client";

import { ReactNode } from "react";

interface TextSliderProps {
  content?: string | number;
  children?: ReactNode;
  className?: string;
}

export default function TextSlider({ content, children, className = "" }: TextSliderProps) {
  return (
    <span className={`oh ${className}`}>
      <span className="oh__inner">
        {content}
        {children}
      </span>
    </span>
  );
}
