"use client";

import { useEffect, useRef } from "react";
import TextSlider from "./TextSlider";

interface TooltipProps {
  grid: string;
}

export default function Tooltip({ grid }: TooltipProps) {
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    let tooltipInstance: any = null;

    const initTooltip = async () => {
      const { default: gsap } = await import("gsap");
      
      class TooltipClass {
        private grid: HTMLElement | null;
        private artists: HTMLCollection | null;
        private tooltip: HTMLElement | null;
        private arrow: HTMLElement | null;
        private OFFSET_X = 20;
        private OFFSET_Y = 0;
        private animationConfig = {
          texts: { duration: 0.7, ease: 'expo' },
          tooltip: { duration: 0.6, ease: 'power4.inOut' },
          textsDelay: 0.4,
          hideDelay: '-=0.7',
        };
        private rowAnimationDirections = {
          stagename: { in: { yPercent: -100 }, out: { yPercent: -100 } },
          name: { in: { yPercent: 100 }, out: { yPercent: 100 } },
          sport: { in: { yPercent: 100 }, out: { yPercent: 100 } },
          arrow: { in: { yPercent: -100 }, out: { yPercent: -100 } },
        };
        private hoverTarget: HTMLElement | null = null;
        private isTooltipVisible = false;
        private scaleDownTimeout: NodeJS.Timeout | undefined;
        private scaleDownTimeline: any;
        private mouseLeaveTimeout: NodeJS.Timeout | undefined;
        private rowTimelines: { [key: string]: any } = {};
        private arrowTimeline: any = null;
        private windowWidth = window.innerWidth;
        private xTo: any;
        private yTo: any;

        constructor(gridEl: HTMLElement) {
          this.grid = gridEl;
          this.artists = this.grid.children;
          if (this.artists.length === 0) return;
          this.tooltip = document.querySelector('.tooltip');
          this.arrow = this.tooltip?.querySelector('.tooltip__row--ra');
          
          this.xTo = gsap.quickTo(this.tooltip, 'x', { duration: 0.6, ease: 'expo' });
          this.yTo = gsap.quickTo(this.tooltip, 'y', { duration: 0.6, ease: 'expo' });

          this.tooltip?.querySelectorAll('.tooltip__row').forEach((row: any) => row.dataset.active = '0');
          this.initializeEvents();
        }

        private initializeEvents() {
          this.grid?.addEventListener('mousemove', this.handleMouseMove);
          window.addEventListener('resize', this.handleResize);

          Array.from(this.artists || []).forEach((artist: any) => {
            artist.addEventListener('mouseenter', this.handleMouseEnter);
            artist.addEventListener('mouseleave', this.handleMouseLeave);
          });
        }

        private handleMouseMove = (e: MouseEvent) => {
          if (!this.hoverTarget || !this.tooltip) return;

          const tooltipWidth = this.tooltip.offsetWidth;
          let tooltipX;
          const tooltipY = e.clientY + this.OFFSET_Y + window.scrollY;

          if (e.clientX + this.OFFSET_X + tooltipWidth > this.windowWidth) {
            tooltipX = e.clientX - this.OFFSET_X - tooltipWidth + window.scrollX;
          } else {
            tooltipX = e.clientX + this.OFFSET_X + window.scrollX;
          }

          if (!this.isTooltipVisible) {
            if (this.scaleDownTimeline) this.scaleDownTimeline.kill();
            clearTimeout(this.scaleDownTimeout);

            gsap.set(this.tooltip, { x: tooltipX, y: tooltipY });
            gsap.fromTo(
              this.tooltip,
              { scale: 0, opacity: 1, transformOrigin: '0% 100%' },
              { ...this.animationConfig.tooltip, scale: 1 }
            );

            this.isTooltipVisible = true;
          } else {
            this.xTo(tooltipX);
            this.yTo(tooltipY);
          }

          clearTimeout(this.scaleDownTimeout);
          this.scaleDownTimeout = setTimeout(() => {
            if (!this.hoverTarget) {
              this.scaleDownTimeline = gsap.timeline();
              this.updateTooltip({ stagename: '', name: '', sport: '' }, this.scaleDownTimeline, 'out');
              this.scaleDownTimeline.to(
                this.tooltip,
                { ...this.animationConfig.tooltip, scale: 0 },
                this.animationConfig.hideDelay
              );
              this.isTooltipVisible = false;
            }
          }, 50);
        };

        private handleMouseEnter = (e: Event) => {
          clearTimeout(this.mouseLeaveTimeout);
          this.hoverTarget = e.currentTarget as HTMLElement;

          if (this.scaleDownTimeline) this.scaleDownTimeline.kill();
          clearTimeout(this.scaleDownTimeout);

          const stageName = this.hoverTarget.dataset.stagename || '';
          const name = this.hoverTarget.dataset.name || '';
          const sport = this.hoverTarget.dataset.sport || '';

          const updateTimeline = gsap.timeline();
          this.updateTooltip({ stagename: stageName, name, sport }, updateTimeline, this.isTooltipVisible ? 'none' : 'in');
        };

        private handleMouseLeave = () => {
          this.hoverTarget = null;

          this.mouseLeaveTimeout = setTimeout(() => {
            if (!this.hoverTarget && this.isTooltipVisible && this.tooltip) {
              gsap.set(this.tooltip, { scale: 0, opacity: 0 });
              this.isTooltipVisible = false;
            }
          }, 50);
        };

        private handleResize = () => {
          this.windowWidth = window.innerWidth;
        };

        private updateTooltip(values: { [key: string]: string }, timeline: any, direction: string) {
          Object.entries(values).forEach(([field, newValue]) => {
            const rowSelector = `[data-field="${field}"]`;
            this.updateTextSlider(rowSelector, newValue, timeline, direction);
          });

          if ((direction === 'in' && !this.isTooltipVisible) || (direction === 'out' && this.isTooltipVisible)) {
            this.animateArrow(timeline, direction);
          }
        }

        private updateTextSlider(rowSelector: string, newValue: string, timeline: any, direction: string) {
          const row = this.tooltip?.querySelector(rowSelector);
          const textSliders = row?.querySelectorAll('.oh__inner');

          if (!textSliders || textSliders.length < 2) return;

          const activeIndex = row?.dataset.active === '0' ? 0 : 1;
          const inactiveIndex = activeIndex === 0 ? 1 : 0;

          const currentSlider = textSliders[activeIndex];
          const nextSlider = textSliders[inactiveIndex];

          const rowField = rowSelector.replace('[data-field="', '').replace('"]', '');
          const animationDirection = this.rowAnimationDirections[rowField as keyof typeof this.rowAnimationDirections] || this.rowAnimationDirections['name'];
          
          const clonedOutDirection = { ...animationDirection.out };
          const clonedInDirection = { ...animationDirection.in };

          if (this.rowTimelines[rowSelector] && direction !== 'out') {
            this.rowTimelines[rowSelector].kill();
          }
          this.rowTimelines[rowSelector] = gsap.timeline();

          if (direction === 'in') {
            gsap.set(currentSlider, clonedOutDirection);
            gsap.set(nextSlider, clonedInDirection);

            this.rowTimelines[rowSelector].to(currentSlider, {
              ...this.animationConfig.texts,
              ...clonedOutDirection,
            }, this.animationConfig.textsDelay);

            gsap.set(nextSlider, clonedInDirection);
            this.rowTimelines[rowSelector].to(nextSlider, {
              ...this.animationConfig.texts,
              yPercent: 0,
              onStart: () => {
                (nextSlider as HTMLElement).textContent = newValue;
              },
            }, this.animationConfig.textsDelay);
          } else if (direction === 'none') {
            const transitionOutDirection = {
              stagename: { yPercent: 100 },
              name: { yPercent: -100 },
              sport: { yPercent: -100 },
            }[rowField as keyof typeof this.rowAnimationDirections] || { yPercent: 0 };

            this.rowTimelines[rowSelector].to(currentSlider, {
              ...this.animationConfig.texts,
              ...transitionOutDirection,
            }, 0);

            gsap.set(nextSlider, clonedInDirection);
            this.rowTimelines[rowSelector].to(nextSlider, {
              ...this.animationConfig.texts,
              yPercent: 0,
              onStart: () => {
                (nextSlider as HTMLElement).textContent = newValue;
              },
            }, 0);
          } else if (direction === 'out') {
            this.rowTimelines[rowSelector].to(currentSlider, {
              ...clonedOutDirection,
              ...this.animationConfig.texts,
            }, 0);
          }

          if (row) {
            row.dataset.active = inactiveIndex.toString();
          }

          timeline.add(this.rowTimelines[rowSelector], 0);
        }

        private animateArrow(timeline: any, direction = 'none') {
          if (!this.arrow) return;

          if (this.arrowTimeline) {
            this.arrowTimeline.kill();
          }
          this.arrowTimeline = gsap.timeline();

          const animationDirection = this.rowAnimationDirections['arrow'];

          if (direction === 'in') {
            this.arrowTimeline.fromTo(this.arrow, {
              ...animationDirection.in,
            }, {
              ...this.animationConfig.texts,
              yPercent: 0,
            }, this.animationConfig.textsDelay);
          } else if (direction === 'out') {
            this.arrowTimeline.to(this.arrow, {
              ...this.animationConfig.texts,
              ...animationDirection.out,
            }, 0);
          }

          timeline.add(this.arrowTimeline, 0);
        }

        destroy() {
          if (this.arrowTimeline) this.arrowTimeline.kill();
          if (this.scaleDownTimeline) this.scaleDownTimeline.kill();
          Object.values(this.rowTimelines).forEach(timeline => timeline && timeline.kill());

          clearTimeout(this.scaleDownTimeout);
          clearTimeout(this.mouseLeaveTimeout);

          this.grid?.removeEventListener('mousemove', this.handleMouseMove);
          window.removeEventListener('resize', this.handleResize);

          Array.from(this.artists || []).forEach((artist: any) => {
            artist.removeEventListener('mouseenter', this.handleMouseEnter);
            artist.removeEventListener('mouseleave', this.handleMouseLeave);
          });
        }
      }

      const gridElement = document.querySelector(`[data-grid="${grid}"]`) as HTMLElement;
      if (gridElement) {
        tooltipInstance = new TooltipClass(gridElement);
      }
    };

    initTooltip();

    return () => {
      if (tooltipInstance) {
        tooltipInstance.destroy();
      }
    };
  }, [grid]);

  return (
    <div ref={tooltipRef} className="tooltip" data-grid={grid}>
      <div className="tooltip__row" data-field="stagename">
        <div className="tooltip__content">
          <TextSlider />
        </div>
        <div className="tooltip__content">
          <TextSlider />
        </div>
      </div>

      <div className="tooltip__row tooltip__row--ra">
        <TextSlider>→</TextSlider>
      </div>

      <div className="tooltip__row" data-field="name">
        <div className="tooltip__content">
          <TextSlider />
        </div>
        <div className="tooltip__content">
          <TextSlider />
        </div>
      </div>

      <div className="tooltip__row tooltip__row--ra" data-field="sport">
        <div className="tooltip__content">
          <TextSlider />
        </div>
        <div className="tooltip__content">
          <TextSlider />
        </div>
      </div>
    </div>
  );
}
