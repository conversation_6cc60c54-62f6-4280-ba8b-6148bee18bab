"use client";

import Image from "next/image";
import Link from "next/link";
import { Champion } from "@/types/champion";
import { useScrollAnimations } from "@/hooks/useScrollAnimations";

interface ChampionDetailProps {
  champion: Champion;
}

export default function ChampionDetail({ champion }: ChampionDetailProps) {
  const containerRef = useScrollAnimations();

  return (
    <div ref={containerRef} className="min-h-screen bg-black text-white">
      {/* Hero Section */}
      <section className="hero-section relative h-screen flex items-center justify-center overflow-hidden">
        <div className="hero-bg absolute inset-0">
          <Image
            src={champion.image}
            alt={champion.name}
            fill
            className="object-cover opacity-30"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black" />
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
          <h1 className="hero-title text-6xl md:text-8xl font-bold mb-4 text-yellow-400">
            {champion.name}
          </h1>
          <p className="hero-subtitle text-2xl md:text-3xl text-gray-300 mb-6">
            {champion.stageName}
          </p>
          <p className="hero-subtitle text-lg text-gray-400 mb-8">
            {champion.sport} • {champion.nationality}
          </p>
          <div className="hero-stats flex justify-center space-x-8 text-center">
            <div>
              <div className="stat-number text-3xl font-bold text-yellow-400">
                {champion.stats.wins}
              </div>
              <div className="text-sm text-gray-400">WINS</div>
            </div>
            <div>
              <div className="stat-number text-3xl font-bold text-yellow-400">
                {champion.stats.losses}
              </div>
              <div className="text-sm text-gray-400">LOSSES</div>
            </div>
            <div>
              <div className="stat-number text-3xl font-bold text-yellow-400">
                {champion.stats.knockouts}
              </div>
              <div className="text-sm text-gray-400">KOs</div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg
            className="w-6 h-6 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        </div>
      </section>

      {/* Introduction */}
      <section className="animate-section py-20 px-6">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-reveal text-4xl font-bold mb-8 text-yellow-400">
            Introduction
          </h2>
          <p className="text-reveal text-xl leading-relaxed text-gray-300">
            {champion.description}
          </p>
        </div>
      </section>

      {/* Achievements Grid */}
      <section className="animate-section py-20 px-6 bg-gray-900">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-reveal text-4xl font-bold mb-12 text-center text-yellow-400">
            Achievements
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {champion.achievements.map((achievement, index) => (
              <div
                key={`achievement-${index}-${achievement.slice(0, 20)}`}
                className="achievement-card bg-black p-6 rounded-lg border border-gray-700"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-sm">
                    {index + 1}
                  </div>
                  <p className="text-gray-300 flex-1">{achievement}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Career Stats */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold mb-12 text-center text-yellow-400">
            Career Statistics
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-white mb-2">
                {champion.stats.fights}
              </div>
              <div className="text-gray-400">Total Fights</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-green-400 mb-2">
                {champion.stats.wins}
              </div>
              <div className="text-gray-400">Wins</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-red-400 mb-2">
                {champion.stats.losses}
              </div>
              <div className="text-gray-400">Losses</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-yellow-400 mb-2">
                {champion.stats.draws}
              </div>
              <div className="text-gray-400">Draws</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-orange-400 mb-2">
                {champion.stats.knockouts}
              </div>
              <div className="text-gray-400">Knockouts</div>
            </div>
          </div>
        </div>
      </section>

      {/* Titles */}
      <section className="animate-section py-20 px-6 bg-gray-900">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-reveal text-4xl font-bold mb-12 text-center text-yellow-400">
            Championship Titles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {champion.titles.map((title, index) => (
              <div
                key={`title-${index}-${title.slice(0, 15)}`}
                className="title-card bg-black p-6 rounded-lg border border-yellow-400/30"
              >
                <h3 className="text-xl font-semibold text-yellow-400 mb-2">
                  {title}
                </h3>
                <p className="text-gray-400">
                  {champion.career.start} - {champion.career.end || "Present"}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Career Timeline */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold mb-12 text-center text-yellow-400">
            Career
          </h2>
          <div className="bg-gray-900 p-8 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <span className="text-gray-400">Career Start</span>
              <span className="text-white font-semibold">
                {champion.career.start}
              </span>
            </div>
            {champion.career.end && (
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-400">Career End</span>
                <span className="text-white font-semibold">
                  {champion.career.end}
                </span>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Status</span>
              <span
                className={`font-semibold capitalize ${
                  champion.career.status === "active"
                    ? "text-green-400"
                    : "text-yellow-400"
                }`}
              >
                {champion.career.status}
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Back to Champions */}
      <section className="py-20 px-6 text-center">
        <Link
          href="/"
          className="inline-flex items-center space-x-2 text-yellow-400 hover:text-white transition-colors border border-yellow-400 hover:border-white px-6 py-3 rounded-lg"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          <span>Back to Champions</span>
        </Link>
      </section>
    </div>
  );
}
