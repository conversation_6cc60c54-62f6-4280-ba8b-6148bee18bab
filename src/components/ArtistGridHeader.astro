---
import TextSlider from "../components/TextSlider.astro";
import Separator from "../components/Separator.astro";
import Search from "../components/Search.astro";
import Cross from "../assets/images/cross.svg";

type Props = {
  artistsTotal: number;
};

const { artistsTotal } = Astro.props;
---

<header class="title-header title-header--initial">
  <h2><TextSlider content="Artists" /></h2>
  <div>
    <button data-sort><TextSlider content="Sort" /></button>
    <button data-shuffle><TextSlider content="Shuffle" /></button>
    <div>
      <button data-search class="search"><TextSlider content="Search" /></button
      >
      <button data-clear class="clear hidden" aria-label="Clear search">
        <Cross width={14} height={14} />
      </button>
    </div>
  </div>
  <span><TextSlider content={artistsTotal} /></span>
</header>

<Separator />
<Search />

<style>
  div {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 1rem;
    margin: 0;
    padding: 0;
  }
  div:has(> .search) {
    position: relative;
  }
  div:has(> .search--active) {
    margin-left: 1rem;
  }
  div:has(> .search--active)::before {
    pointer-events: none;
    content: "";
    position: absolute;
    left: -1rem;
    right: -1rem;
    top: -0.5rem;
    bottom: -0.5rem;
    border: 1px solid;
    border-radius: 2rem;
  }
  .search--active {
    &:focus-visible {
      color: inherit;
    }
  }
</style>
<script src="../scripts/gridActions.js"></script>
