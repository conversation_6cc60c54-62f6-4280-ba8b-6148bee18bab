"use client";

import { useEffect, useState } from "react";

export default function Preloader() {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Check if preload was already completed in this session
    if (sessionStorage.getItem('preloadComplete') === 'true') {
      setIsVisible(false);
      return;
    }

    const loadImages = () => {
      return new Promise((resolve, reject) => {
        // Collect all <img> elements
        const imgElements = document.querySelectorAll('img');

        // Collect elements with background images
        const bgElements = [...document.querySelectorAll('*')].filter((el) => {
          const style = window.getComputedStyle(el);
          return style.backgroundImage !== 'none';
        });

        // Combine both sets of elements
        const allElements = [...imgElements, ...bgElements];

        if (allElements.length === 0) {
          resolve(true);
          return;
        }

        let loadedCount = 0;
        const totalCount = allElements.length;

        const checkComplete = () => {
          loadedCount++;
          if (loadedCount >= totalCount) {
            resolve(true);
          }
        };

        // Load regular images
        imgElements.forEach((img) => {
          if (img.complete) {
            checkComplete();
          } else {
            img.onload = checkComplete;
            img.onerror = checkComplete;
          }
        });

        // Load background images
        bgElements.forEach((el) => {
          const style = window.getComputedStyle(el);
          const bgImage = style.backgroundImage;
          
          if (bgImage && bgImage !== 'none') {
            const matches = bgImage.match(/url\(["']?([^"']*)["']?\)/);
            if (matches && matches[1]) {
              const img = new Image();
              img.onload = checkComplete;
              img.onerror = checkComplete;
              img.src = matches[1];
            } else {
              checkComplete();
            }
          } else {
            checkComplete();
          }
        });
      });
    };

    const loadAssets = async () => {
      try {
        await loadImages();
        
        // Dispatch custom event for animations
        const event = new CustomEvent('assetsLoaded');
        document.dispatchEvent(event);
        
        // Mark as complete and hide preloader
        sessionStorage.setItem('preloadComplete', 'true');
        setIsVisible(false);
      } catch (error) {
        console.error('Failed to load assets:', error);
        setIsVisible(false);
      }
    };

    // Small delay to ensure DOM is ready
    setTimeout(loadAssets, 100);

    // Clear preload flag on page unload to show loader on refresh
    const handleBeforeUnload = () => {
      sessionStorage.removeItem('preloadComplete');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  if (!isVisible) return null;

  return (
    <div className="loading">
      <div className="loading__content">
        <div className="loading__spinner"></div>
        <p>Loading Champions...</p>
      </div>
    </div>
  );
}
