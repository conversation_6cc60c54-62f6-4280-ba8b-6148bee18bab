"use client";

import { useState, useEffect, useRef } from "react";

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState("");
  const dialogRef = useRef<HTMLDialogElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
      // Add blur effect to page
      document.body.classList.add("blurred");
      // Focus input after modal opens
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      dialog.close();
      document.body.classList.remove("blurred");
    }
  }, [isOpen]);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    const handleClose = () => {
      setQuery("");
      document.body.classList.remove("blurred");
      onClose();
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        handleClose();
      }
    };

    dialog.addEventListener("close", handleClose);
    document.addEventListener("keydown", handleEscape);

    return () => {
      dialog.removeEventListener("close", handleClose);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [onClose]);

  const handleClose = () => {
    setQuery("");
    document.body.classList.remove("blurred");
    onClose();
  };

  return (
    <dialog ref={dialogRef} id="search-dialog">
      <form method="dialog">
        <label htmlFor="search-input" className="sr-only">
          Search:
        </label>
        <input
          ref={inputRef}
          aria-label="search"
          type="text"
          id="search-input"
          placeholder="Search"
          maxLength={30}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
        <span>Enter a name or stage name</span>
        <button
          id="close-dialog"
          aria-label="Close search"
          onClick={handleClose}
          type="button"
        >
          <svg width={9} height={9} viewBox="0 0 9 9" fill="currentColor">
            <path
              d="M8.5 0.5L0.5 8.5M0.5 0.5L8.5 8.5"
              stroke="currentColor"
              strokeWidth="1"
            />
          </svg>
        </button>
      </form>
    </dialog>
  );
}
