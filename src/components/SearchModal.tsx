"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { searchChampions } from "@/data/champions";
import { Champion } from "@/types/champion";

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<Champion[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const searchResults = searchChampions(query);
    setResults(searchResults);
  }, [query]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleClose = () => {
    setQuery("");
    onClose();
  };

  const handleResultClick = () => {
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="search-modal" onClick={handleClose}>
      <div
        className="search-modal__content"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          onClick={handleClose}
          className="search-modal__close"
          aria-label="Close search"
        >
          ✕
        </button>

        <input
          ref={inputRef}
          type="text"
          placeholder="Search"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="search-modal__input"
        />

        <span className="search-modal__hint">Enter a name or stage name</span>

        {results.length > 0 && (
          <div
            style={{ marginTop: "1rem", maxHeight: "240px", overflowY: "auto" }}
          >
            {results.map((champion) => (
              <Link
                key={champion.id}
                href={`/champion/${champion.slug}`}
                onClick={handleResultClick}
                style={{
                  display: "block",
                  width: "100%",
                  textAlign: "left",
                  padding: "0.5rem 0",
                  borderBottom: "1px solid var(--color-faded)",
                  textDecoration: "none",
                  color: "inherit",
                }}
              >
                <div style={{ fontWeight: "bold" }}>{champion.name}</div>
                <div
                  style={{
                    fontSize: "var(--font-size-s)",
                    color: "var(--color-faded)",
                  }}
                >
                  {champion.stageName}
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
