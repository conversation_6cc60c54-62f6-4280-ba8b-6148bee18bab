"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { searchChampions } from "@/data/champions";
import { Champion } from "@/types/champion";

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<Champion[]>([]);
  const dialogRef = useRef<HTMLDialogElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
      // Add blur effect to page
      document.body.classList.add("blurred");
      // Focus input after modal opens
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      dialog.close();
      document.body.classList.remove("blurred");
    }
  }, [isOpen]);

  useEffect(() => {
    const searchResults = searchChampions(query);
    setResults(searchResults);
  }, [query]);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    const handleClose = () => {
      setQuery("");
      document.body.classList.remove("blurred");
      onClose();
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        handleClose();
      }
    };

    dialog.addEventListener("close", handleClose);
    document.addEventListener("keydown", handleEscape);

    return () => {
      dialog.removeEventListener("close", handleClose);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [onClose]);

  const handleClose = () => {
    setQuery("");
    document.body.classList.remove("blurred");
    onClose();
  };

  const handleResultClick = () => {
    handleClose();
  };

  return (
    <dialog ref={dialogRef} id="search-dialog">
      <form method="dialog">
        <label htmlFor="search-input" className="sr-only">
          Search:
        </label>
        <input
          ref={inputRef}
          aria-label="search"
          type="text"
          id="search-input"
          placeholder="Search"
          maxLength={30}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
        <span>Enter a name or stage name</span>
        <button
          id="close-dialog"
          aria-label="Close search"
          onClick={handleClose}
          type="button"
        >
          ✕
        </button>

        {results.length > 0 && (
          <div
            style={{ marginTop: "1rem", maxHeight: "240px", overflowY: "auto" }}
          >
            {results.map((champion) => (
              <Link
                key={champion.id}
                href={`/champion/${champion.slug}`}
                onClick={handleResultClick}
                style={{
                  display: "block",
                  width: "100%",
                  textAlign: "left",
                  padding: "0.5rem 0",
                  borderBottom: "1px solid var(--color-faded)",
                  textDecoration: "none",
                  color: "inherit",
                }}
              >
                <div style={{ fontWeight: "bold" }}>{champion.name}</div>
                <div
                  style={{
                    fontSize: "var(--font-size-s)",
                    color: "var(--color-faded)",
                  }}
                >
                  {champion.stageName}
                </div>
              </Link>
            ))}
          </div>
        )}
      </form>
    </dialog>
  );
}
