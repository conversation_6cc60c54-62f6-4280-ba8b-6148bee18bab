* {
  box-sizing: border-box;

  &::after,
  &::before {
    box-sizing: inherit;
  }
}

:root {
  font-size: 16px;
  --color-text: #000;
  --color-text-alt: #6a6a6a;
  --color-faded: #c9c9c9;
  --color-bg: #fff;
  --border-color: #ccc;
  --color-link: #000;
  --color-link-hover: #999;
  --color-placeholder: #f8f8f8;
  --font-size-s: 0.85rem;
  --font-size-l: clamp(1.25rem, 3vw, 1.75rem);
  --font-size-xl: clamp(1.5rem, 5vw, 2.75rem);
}

body {
  margin: 0;
  padding: 0 1rem;
  color: var(--color-text);
  background-color: var(--color-bg);
  line-height: 1;
  font-family: 'Instrument Sans Variable', serif;
  font-variant-ligatures: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &.blurred {
    filter: blur(5px);
    pointer-events: none;
  }
}

h2 {
  font-size: var(--font-size-xl);
  margin: 0;
  line-height: 0.85;
}

h3 {
  font-size: 1rem;
  margin-bottom: 2.5rem;
}

p+h3 {
  margin-top: 4rem;
}

a {
  text-decoration: none;
  color: var(--color-link);
  outline: none;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: var(--color-link-hover);
    outline: none;
  }

  &:focus {
    outline: none;
    background-color: lightgrey;

    &:not(:focus-visible) {
      background-color: transparent;
    }

    &:focus-visible {
      opacity: 0.5;
      background-color: transparent;
    }
  }
}

img {
  display: block;
}

.fade-in {
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

.fade-in.loaded {
  opacity: 1;
}

button {
  background: none;
  display: flex;
  padding: 0;
  border: 0;
  cursor: pointer;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  fill: currentColor;

  &:hover {
    color: var(--color-link-hover);
  }

  &:focus {
    outline: none;
    background-color: lightgrey;

    &:not(:focus-visible) {
      background-color: transparent;
    }

    &:focus-visible {
      background-color: transparent;
      color: var(--color-link-hover);
    }
  }
}

img {
  max-width: 100%;
}

.hidden {
  opacity: 0;
  pointer-events: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.faded {
  color: var(--color-faded);
}

.inline {
  display: flex;
  gap: 1rem;
}

.content-wrap {
  display: grid;
  gap: 2rem;
  padding: 3rem 0 10vh;
  grid-template-columns: 100%;
  grid-auto-columns: auto;
  grid-template-areas: 'img' 'content';
}

@media screen and (min-width: 44em) {
  .content-wrap {
    grid-template-columns: 35vw 1fr;
    grid-template-areas: 'img content';
  }
}

.content {
  grid-area: content;
  max-width: 500px;
}

.content--page {
  max-width: none;
  min-height: calc(100vh - 10rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  @media screen and (min-width: 50em) {
    margin: 0 20vw;
    padding: 3rem 1rem;
  }
}

.content p,
.content ol {
  line-height: 1.4;
  font-weight: 500;
}

.content ol {
  list-style-type: decimal-leading-zero;
  padding: 0 0 0 1.8em;
  margin-bottom: 2rem;
}

dl {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 1rem;
  margin-top: 2.5rem;
}

dt {
  font-weight: bold;
  grid-column: 1;
}

dd {
  grid-column: 2;
  margin: 0;
}

.image {
  background-color: var(--color-placeholder);
  grid-area: img;
  margin-top: 1rem;
}

.title-header {
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  font-weight: 700;
  gap: 1rem;
  align-items: end;
  padding: 2rem 0 1.5rem;
  width: 100%;
}

.title-header--initial {
  font-size: var(--font-size-l);

  @media screen and (min-width: 50em) {
    grid-template-columns: 20vw 1fr auto;
    gap: 1rem;
  }
}