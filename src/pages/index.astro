---
import { getCollection } from "astro:content";
import BaseLayout from "../layouts/BaseLayout.astro";
import ArtistGrid from "../components/ArtistGrid.astro";
import ArtistGridHeader from "../components/ArtistGridHeader.astro";
import Preloader from "../components/Preloader.astro";

const allArtists = await getCollection("artists");
const artistsTotal = allArtists.length;
const title = "Players Club - All Artists";
---

<BaseLayout title={title}>
  <!-- 
    The <Preloader /> component is used to preload all images on the homepage using the images.loaded.js script. 
    If you don’t want to use the preloader (e.g., for faster page load times or if you don’t require preloading images), you can safely remove the <Preloader /> component from this layout.
  -->
  <Preloader />
  <ArtistGridHeader artistsTotal={artistsTotal} />
  <ArtistGrid artists={allArtists} />
</BaseLayout>
<script src="../scripts/index.js"></script>
