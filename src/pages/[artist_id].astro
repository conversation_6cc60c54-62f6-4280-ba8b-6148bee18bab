---
import type { CollectionEntry } from "astro:content";
import { getCollection, render } from "astro:content";
import BaseLayout from "../layouts/BaseLayout.astro";
import Separator from "../components/Separator.astro";
import AlbumGrid from "../components/AlbumGrid.astro";

export const getStaticPaths = async () => {
  // Fetch all artists from the 'artists' collection
  const artists = await getCollection("artists");

  // Map over the artists to generate paths for each artist's page
  const paths = artists.map((artist) => {
    return {
      params: { artist_id: artist.id }, // URL parameter for the artist's ID
      props: { artists, artist }, // Pass the full artist and all artists as props
    };
  });

  // Return the generated paths to Astro for static page generation
  return paths;
};

type Props = {
  artists: CollectionEntry<"artists">[]; // Array of all artists in the collection
  artist: CollectionEntry<"artists">; // Current artist being rendered
};

const { artists, artist } = Astro.props; // Extract the artists list and current artist from the props

// Extract the ID of the current artist
const artist_id = artist.id;

// Find the index of the current artist in the artists array
const currentArtistIndex = artists.findIndex((a) => a.id === artist_id);

// Calculate the index of the "next" artist in the array (wrap around if at the last artist)
const nextArtistIndex = (currentArtistIndex + 1) % artists.length;

// Get the "next" artist based on the calculated index
const nextArtist = artists[nextArtistIndex];

// Fetch all albums that belong to the current artist
const albums = await getCollection(
  "albums",
  ({ data }) => data.artist.id === artist_id,
);

// Render the content of the current artist (e.g., markdown or MDX)
const { Content } = await render(artist);
---

<BaseLayout title={artist.data.name}>
  <section>
    <header class="title-header">
      <h2>
        {artist.data.stage_name}
        <span class="faded">({artist.data.name})</span>
      </h2>
      <div>
        <a class="back" href="/">View all</a>
        <span class="faded">/</span>
        <a href={`/${nextArtist.id}`}>Next artist</a>
      </div>
    </header>
    <Separator />
    <div class="content-wrap">
      <img
        class="image fade-in"
        src={artist.data.image.src}
        alt={artist.data.image.alt}
      />
      <div class="content">
        <h3>Biography</h3>
        <Content />
        <dl>
          <dt>Genre</dt><dd>{artist.data.genre}</dd>
        </dl>
      </div>
      {
        albums.length > 0 && (
          <div class="albums">
            <h3>Releases</h3>
            <AlbumGrid albums={albums} />
          </div>
        )
      }
    </div>
  </section>
</BaseLayout>
<style>
  .content-wrap {
    grid-template-areas: "img" "content" "albums";
    grid-template-columns: 100%;
    @media screen and (min-width: 44em) {
      grid-template-columns: 1fr auto;
      grid-template-areas: "img img" "content albums";
    }
    @media screen and (min-width: 50em) {
      grid-template-columns: 30vw 1fr 170px;
      grid-template-areas: "img content albums";
    }
  }
  .albums {
    grid-area: albums;
  }
  .content p:last-child {
    margin-top: 2.5rem;
    display: flex;
  }
</style>
