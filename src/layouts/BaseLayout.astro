---
import { ClientRouter } from "astro:transitions";

import "../styles/global.css";

import { SEO } from "astro-seo";

import SmoothScroll from "../components/SmoothScroll.astro";
import Header from "../components/Header.astro";
import Footer from "../components/Footer.astro";

import "@fontsource-variable/instrument-sans";
import instrumentSansWoff2 from "@fontsource-variable/instrument-sans/files/instrument-sans-latin-wght-normal.woff2";

type Props = {
  title: string;
  description?: string;
};

const { title, description = "Players Club Concept" } = Astro.props;

// Current page value (/home, /about, /404 ...)
const currentPage =
  Astro.url.pathname.replace(/\/$/, "").split("/").pop() || "home";
---

<!doctype html>
<html lang="en" data-page={currentPage}>
  <head>
    <SEO
      title={title}
      description={description}
      charset="UTF-8"
      openGraph={{
        basic: {
          title: title,
          type: "image/jpeg",
          image: "/playersclub-og.jpg",
        },
      }}
      twitter={{
        creator: "@codrops",
      }}
      extend={{
        // extending the default link tags
        link: [
          {
            rel: "icon",
            type: "image/png",
            sizes: "16x16",
            href: "/favicon/favicon-16x16.png",
          },
          {
            rel: "icon",
            type: "image/png",
            sizes: "32x32",
            href: "/favicon/favicon-32x32.png",
          },
          {
            rel: "apple-touch-icon",
            sizes: "180x180",
            href: "/favicon/apple-touch-icon.png",
          },
          { rel: "manifest", href: "/favicon/site.webmanifest" },
          { rel: "sitemap", href: "/sitemap-index.xml" },
          {
            rel: "preload",
            href: instrumentSansWoff2,
            as: "font",
            type: "font/woff2",
            crossorigin: "anonymous",
          },
        ],
        // extending the default meta tags
        meta: [
          { name: "twitter:image", content: "/playersclub-og.jpg" },
          { name: "twitter:title", content: title },
          { name: "twitter:description", content: description },
          { name: "viewport", content: "width=device-width" },
          { name: "msapplication-TileColor", content: "#000000" },
          { name: "theme-color", content: "#ffffff" },
          { name: "generator", content: Astro.generator },
        ],
      }}
    />
    <ClientRouter />
  </head>
  <body>
    <SmoothScroll />
    <Header />
    <slot />
    <Footer />
    <script src="../scripts/imageFade.js"></script>
  </body>
</html>
