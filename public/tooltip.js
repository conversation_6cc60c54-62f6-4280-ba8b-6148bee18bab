// Tooltip functionality for PlayersClub-style hover effects
document.addEventListener("DOMContentLoaded", function () {
  const tooltip = document.querySelector(".tooltip");
  const grid = document.querySelector('[data-grid="champions"]');
  const gridItems = grid?.querySelectorAll("a[data-name]");

  if (!tooltip || !grid || !gridItems) return;

  let currentItem = null;

  function updateTooltipContent(item) {
    const name = item.getAttribute("data-name");
    const stageName = item.getAttribute("data-stagename");
    const sport = item.getAttribute("data-sport");

    // Update stage name (first row)
    const stageNameContents = tooltip.querySelectorAll(
      '[data-field="stagename"] .tooltip__content'
    );
    stageNameContents.forEach((content) => {
      content.textContent = stageName || "";
    });

    // Update name (third row)
    const nameContents = tooltip.querySelectorAll(
      '[data-field="name"] .tooltip__content'
    );
    nameContents.forEach((content) => {
      content.textContent = name || "";
    });

    // Update sport (fourth row)
    const sportContents = tooltip.querySelectorAll(
      '[data-field="sport"] .tooltip__content'
    );
    sportContents.forEach((content) => {
      content.textContent = sport || "";
    });
  }

  function showTooltip(item, event) {
    if (currentItem === item) return;

    currentItem = item;
    updateTooltipContent(item);

    // Position tooltip near mouse
    const rect = item.getBoundingClientRect();
    tooltip.style.left = `${rect.right + 10}px`;
    tooltip.style.top = `${rect.top}px`;
    tooltip.style.opacity = "1";
    tooltip.style.transform = "translateX(0)";
  }

  function hideTooltip() {
    currentItem = null;
    tooltip.style.opacity = "0";
    tooltip.style.transform = "translateX(-10px)";
  }

  // Add event listeners to grid items
  gridItems.forEach((item) => {
    item.addEventListener("mouseenter", (e) => showTooltip(item, e));
    item.addEventListener("mouseleave", hideTooltip);
  });

  // Hide tooltip when mouse leaves grid
  grid.addEventListener("mouseleave", hideTooltip);
});
